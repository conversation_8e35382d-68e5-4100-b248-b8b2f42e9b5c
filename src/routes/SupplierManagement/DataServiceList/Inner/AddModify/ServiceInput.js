import I18N from '@/utils/I18N';
import { PureComponent, Fragment, memo } from 'react';
import { connect } from 'dva';
import { cloneDeep, isEqual } from 'lodash';
import { Input, Select, Table, Icon, Tooltip, Checkbox, Pagination } from 'tntd';

import TooltipSelect from '@tntd/tooltip-select';

import otp from './otp';

const Option = Select.Option;

// TooltipSelect的高阶组件
let MemoSelect = memo(
    (props) => {
        return <TooltipSelect {...props}>{props.children}</TooltipSelect>;
    },
    (prevProps, nextProps) => {
        if (
            prevProps.value === nextProps.value &&
            prevProps.disabled === nextProps.disabled &&
            isEqual(prevProps.options, nextProps.options)
        ) {
            return true;
        }
        return false;
    }
);

class ServiceInput extends PureComponent {
    state = {
        inputPage: {
            curPage: 1,
            pageSize: 10
        },
        inputConfig: [],
        serviceInputIsNull: false,
        isShowErrMsg: {}
    };

    // 触发Form的onChange和校验
    triggerChange = (changedValue) => {
        const { onChange } = this.props;
        if (onChange) {
            onChange(changedValue);
        }
    };

    // 添加服务入参
    addInputData = () => {
        const { inputPage } = this.state;
        const { isSync, current, onChange } = this.props;

        const { inputConfig } = this.state;

        let paramsItem = {
            displayName: null, // 字段名称
            serviceParam: null, // 字段标识
            type: 'variable', // 字段类型(constant常量/variable变量)
            value: null, // 值类型：type=constant时的常量值
            dataType: null, // 值类型(1字符型/2整型/3小数型/4布尔型/5日期型)
            mustInput: false, // 是否必填
            sendSpace: null, // 类型
            keyInclude: false // 缓存key
        };

        if (this.props.isSql && isSync) {
            paramsItem.sendSpace = 'sqlParam';
        }

        if (current === 1) {
            let copyInputConfig = cloneDeep(inputConfig);
            copyInputConfig.push(paramsItem);
            copyInputConfig.forEach((item, index) => {
                item.uuid = index;
            });

            const curPage = Math.ceil(copyInputConfig.length / inputPage.pageSize);
            this.setState({
                inputPage: {
                    curPage,
                    pageSize: inputPage.pageSize
                },
                inputConfig: copyInputConfig
            });
            this.triggerChange(copyInputConfig);
        } else {
            let copyAsyncInputConfig = cloneDeep(inputConfig);
            copyAsyncInputConfig.push({ ...paramsItem, type: 'constant', mustConstant: true });
            copyAsyncInputConfig.forEach((item, index) => {
                item.uuid = index;
            });

            const curPage = Math.ceil(copyAsyncInputConfig.length / inputPage.pageSize);
            this.setState({
                inputPage: {
                    curPage,
                    pageSize: inputPage.pageSize
                },
                inputConfig: copyAsyncInputConfig
            });
            this.triggerChange(copyAsyncInputConfig);
        }
    };

    // 删除服务入参
    deleteInput(uuid) {
        const { current, onChange } = this.props;

        const { inputConfig } = this.state;

        if (current === 1) {
            let copyInputConfig = cloneDeep(inputConfig);
            copyInputConfig.splice(uuid, 1);
            copyInputConfig.forEach((item, index) => {
                item.uuid = index;
            });

            let InputConfigFlag;
            copyInputConfig.forEach((item) => {
                if (!item.serviceParam || !item.sendSpace || !item.dataType || !item.type || !item.field) {
                    InputConfigFlag = false;
                } else {
                    InputConfigFlag = true;
                }
            });
            if (!InputConfigFlag) {
                this.setState({
                    serviceInputIsNull: false
                });
            }
            if (InputConfigFlag) {
                this.setState({
                    serviceInputIsNull: true
                });
            }

            this.setState({
                inputConfig: copyInputConfig
            });

            this.triggerChange(copyInputConfig);
        } else {
            let copyAsyncInputConfig = cloneDeep(inputConfig);
            copyAsyncInputConfig.splice(uuid, 1);
            copyAsyncInputConfig.forEach((item, index) => {
                item.uuid = index;
            });

            let AsyncInputConfigFlag;
            inputConfig.forEach((item) => {
                if (
                    !item.serviceParam ||
                    !item.sendSpace ||
                    !item.type ||
                    !item.value ||
                    !item.serviceParam ||
                    !item.sendSpace ||
                    !item.type ||
                    !item.field
                ) {
                    AsyncInputConfigFlag = false;
                } else {
                    AsyncInputConfigFlag = true;
                }
            });

            if (!AsyncInputConfigFlag) {
                this.setState({
                    serviceInputIsNull: true
                });
            } else {
                this.setState({
                    serviceInputIsNull: false
                });
            }
            this.setState({
                inputConfig: copyAsyncInputConfig
            });

            this.triggerChange(copyAsyncInputConfig);
        }
    }

    componentDidMount() {
        const { value = [] } = this.props;

        this.setState({
            inputConfig: value
        });
    }

    componentDidUpdate(prevProps) {
        if (prevProps.value !== this.props.value) {
            const newInputConfig = this.props.value || [];
            const { inputPage } = this.state;

            // 如果数据长度发生变化，需要重新计算当前页
            const totalPages = Math.ceil(newInputConfig.length / inputPage.pageSize);
            const newCurPage = totalPages === 0 ? 1 : Math.min(inputPage.curPage, totalPages);

            this.setState({
                inputConfig: newInputConfig,
                inputPage: {
                    curPage: newCurPage,
                    pageSize: inputPage.pageSize
                }
            });
        }
    }

    // 改变服务入参
    changeInputField(e, type, field, uuid, index) {
        const { current, onChange } = this.props;
        const { isShowErrMsg, inputConfig } = this.state;
        let val = null;
        if (type === 'select') {
            val = e;
            if (val === null || val === undefined) {
                isShowErrMsg[field + index] = 2;
            } else {
                isShowErrMsg[field + index] = 1;
            }
        }
        if (type === 'input') {
            val = e.target.value;
            if (val !== '') {
                isShowErrMsg[field + index] = 1;
            } else {
                isShowErrMsg[field + index] = 2;
            }
        }
        if (type === 'checkbox') {
            val = e.target.checked;
        }

        if (current === 1) {
            let copyInputConfig = cloneDeep(inputConfig);
            if (field === 'type') {
                copyInputConfig[uuid]['value'] = null;
                copyInputConfig[uuid]['dataType'] = null;
                copyInputConfig[uuid]['field'] = null;
                copyInputConfig[uuid]['keyInclude'] = null;
            }
            if (field === 'field') {
                const systemField = (this.props.systemList || []).find(({ name }) => name === val);
                if (systemField) {
                    copyInputConfig[uuid].dataType = systemField.dataType;
                }
            }
            copyInputConfig[uuid][field] = val;

            this.setState({
                inputConfig: copyInputConfig
            });
            this.triggerChange(copyInputConfig);
        } else {
            let copyAsyncInputConfig = cloneDeep(inputConfig);
            if (field === 'type') {
                copyAsyncInputConfig[uuid]['value'] = null;
                copyAsyncInputConfig[uuid]['dataType'] = null;
                copyAsyncInputConfig[uuid]['field'] = null;
                copyAsyncInputConfig[uuid]['keyInclude'] = null;
            }
            if (field === 'field') {
                const systemField = (this.props.systemList || []).find(({ name }) => name === val);
                if (systemField) {
                    copyAsyncInputConfig[uuid].dataType = systemField.dataType;
                }
            }
            copyAsyncInputConfig[uuid][field] = val;

            this.setState({
                inputConfig: copyAsyncInputConfig
            });
            this.triggerChange(copyAsyncInputConfig);
        }

        this.setState({
            serviceInputIsNull: true
        });
    }
    // 校验输入框函数
    changeFieldShowErrMsg = (field, flag, index) => {
        const { isShowErrMsg } = this.state;

        isShowErrMsg[field + index] = flag;

        this.setState({
            isShowErrMsg
        });
    };

    // 服务入参分页
    inputOnChange = (curPage, pageSize) => {
        this.setState({
            inputPage: {
                curPage,
                pageSize
            }
        });
    };

    getColumns = (modalType, disabled) => {
        const { systemList, isSync, current } = this.props;

        const { inputConfig, isShowErrMsg } = this.state;

        let columns = [
            {
                title: (
                    <Fragment>
                        {/* 系统字段 */}
                        {I18N.addmodify.serviceinput.xiTongZiDuan}
                        <Tooltip title={I18N.addmodify.serviceinput.hePingTaiTiGong}>
                            <Icon type="question-circle" />
                        </Tooltip>
                    </Fragment>
                ),
                dataIndex: 'field',
                width: 200,
                render: (text, record, index) => {
                    let dom = (
                        <MemoSelect
                            options={systemList}
                            isVirtual
                            onBlur={() => {
                                if (record.field === null || record.field === undefined) {
                                    this.changeFieldShowErrMsg('field', 2, index);
                                } else {
                                    this.changeFieldShowErrMsg('field', 1, index);
                                }
                            }}
                            className={isShowErrMsg['field' + index] === 2 ? 'highlightInput' : ''}
                            showSearch
                            style={{ width: isSync ? '200px' : '172px' }}
                            disabled={disabled || record.fromContract}
                            // dropdownMatchSelectWidth={false}
                            // dropdownStyle={{ width: 350 }}
                            optionFilterProp="children"
                            value={record.field ? record.field : undefined}
                            onChange={(e) => this.changeInputField(e, 'select', 'field', record.uuid, index)}>
                            {systemList &&
                                systemList.map((item, index) => {
                                    let disabled = inputConfig.findIndex((i) => i.field === item.name) > -1;
                                    return (
                                        <Option value={item.name} key={index} disabled={disabled}>
                                            {item.displayName}【{item.name}】
                                        </Option>
                                    );
                                })}
                        </MemoSelect>
                    );
                    if (record.type === 'constant') {
                        dom = (
                            <Tooltip title={record.value}>
                                <Input
                                    onBlur={() => {
                                        if (record.value === null || record.value === '') {
                                            this.changeFieldShowErrMsg('value', 2, index);
                                        } else {
                                            this.changeFieldShowErrMsg('value', 1, index);
                                        }
                                    }}
                                    className={isShowErrMsg['value' + index] === 2 ? 'highlightInput' : ''}
                                    disabled={disabled}
                                    value={record.value}
                                    placeholder={I18N.addmodify.serviceinput.qingShuRuDingZhi} // 请输入定值
                                    onChange={(e) => this.changeInputField(e, 'input', 'value', record.uuid, index)}
                                />
                            </Tooltip>
                        );
                    }
                    return dom;
                }
            },
            {
                title: (
                    <Fragment>
                        {/* 映射 */}
                        {I18N.addmodify.serviceinput.yingShe}
                        <Tooltip title={I18N.addmodify.serviceinput.baXiTongZiDuan}>
                            <Icon type="question-circle" />
                        </Tooltip>
                    </Fragment>
                ),
                dataIndex: 'diraction',
                width: otp.diraction,
                align: 'center',
                render: () => {
                    return <Icon type="arrow-right" />;
                }
            },
            {
                title: I18N.addmodify.serviceinput.canShuBiaoZhi, // 参数标识
                dataIndex: 'serviceParam',
                key: 'serviceParam',
                // width: !isSync ? '80px' : '100px',
                width: 100,
                render: (text, record, index) => {
                    let dom = (
                        <Tooltip title={text}>
                            <Input
                                onBlur={() => {
                                    if (text === null || text === undefined || text === '') {
                                        this.changeFieldShowErrMsg('serviceParam', 2, index);
                                    } else {
                                        this.changeFieldShowErrMsg('serviceParam', 1, index);
                                    }
                                }}
                                className={isShowErrMsg['serviceParam' + index] === 2 || record.inputFlag ? 'highlightInput' : ''}
                                disabled={disabled || (isSync && current === 1 && record.sendSpace === 'sql' && this.props.isSql)}
                                value={text}
                                onChange={(e) => this.changeInputField(e, 'input', 'serviceParam', record.uuid, index)}
                            />
                        </Tooltip>
                    );
                    return dom;
                }
            },
            {
                title: I18N.addmodify.serviceinput.canShuLeiXing, // 参数类型
                dataIndex: 'type',
                key: 'type',
                width: 100,
                render: (text, record, index) => {
                    let dom = (
                        <TooltipSelect
                            isVirtual
                            onBlur={() => {
                                if (text === null || text === undefined) {
                                    this.changeFieldShowErrMsg('text', 2, index);
                                } else {
                                    this.changeFieldShowErrMsg('text', 1, index);
                                }
                            }}
                            className={isShowErrMsg['text' + index] === 2 ? 'highlightInput' : ''}
                            showSearch
                            style={{ width: '100%' }}
                            disabled={
                                disabled ||
                                record.fromContract ||
                                // (current === 2 && record.mustConstant) ||
                                (isSync && current === 1 && record.sendSpace === 'sql' && this.props.isSql) // 同步服务，入参sql参数不可编辑
                            }
                            optionFilterProp="children"
                            value={text ? text : undefined}
                            onChange={(e) => this.changeInputField(e, 'select', 'type', record.uuid, index)}>
                            <Option value={'variable'}>
                                {I18N.addmodify.serviceinput.bianLiang}
                                {/* 变量 */}
                            </Option>
                            <Option value={'constant'}>
                                {I18N.addmodify.serviceinput.dingZhi}
                                {/* 定值 */}
                            </Option>
                        </TooltipSelect>
                    );
                    return dom;
                }
            },
            {
                title: I18N.addmodify.serviceinput.zhiLeiXing, // 值类型
                dataIndex: 'value',
                key: 'value',
                width: 100,
                render: (text, record, index) => {
                    let dom = (
                        <TooltipSelect
                            isVirtual
                            onBlur={() => {
                                if (record.dataType === null || record.dataType === undefined) {
                                    this.changeFieldShowErrMsg('dataType', 2, index);
                                } else {
                                    this.changeFieldShowErrMsg('dataType', 1, index);
                                }
                            }}
                            className={isShowErrMsg['dataType' + index] === 2 ? 'highlightInput' : ''}
                            showSearch
                            style={{ width: '100%' }}
                            disabled={true}
                            optionFilterProp="children"
                            value={record.dataType ? record.dataType : undefined}
                            onChange={(e) => this.changeInputField(e, 'select', 'dataType', record.uuid, index)}>
                            <Option value={1}>
                                {I18N.addmodify.serviceinput.ziFuXing}
                                {/* 字符型 */}
                            </Option>
                            <Option value={2}>
                                {I18N.addmodify.serviceinput.zhengXing}
                                {/* 整型 */}
                            </Option>
                            <Option value={3}>
                                {I18N.addmodify.serviceinput.xiaoShuXing}
                                {/* 小数型 */}
                            </Option>
                            <Option value={4}>
                                {I18N.addmodify.serviceinput.riQiXing}
                                {/* 日期型 */}
                            </Option>
                            <Option value={5}>
                                {I18N.addmodify.serviceinput.buErXing}
                                {/* 布尔型 */}
                            </Option>
                        </TooltipSelect>
                    );
                    if (record.type === 'constant') {
                        dom = '--';
                    }
                    return dom;
                }
            },
            {
                title: I18N.addmodify.serviceinput.biTian, // 是否必填
                dataIndex: 'mustInput',
                key: 'mustInput',
                width: 60,
                render: (text, record) => {
                    let dom = (
                        <Checkbox
                            disabled={disabled || record.fromContract}
                            checked={text}
                            onChange={(e) => this.changeInputField(e, 'checkbox', 'mustInput', record.uuid)}
                        />
                    );
                    return record.type === 'variable' ? dom : '--';
                }
            },
            {
                title: (
                    <Fragment>
                        {/* 查询条件 */}
                        {I18N.addmodify.serviceinput.chaXunTiaoJian}
                        <Tooltip title={I18N.addmodify.serviceinput.sheZhiDiYiBu}>
                            <Icon type="question-circle" />
                        </Tooltip>
                    </Fragment>
                ),
                dataIndex: 'keyInclude',
                key: 'keyInclude',
                width: otp.keyInclude,
                render: (text, record) => {
                    let dom = (
                        <Checkbox
                            disabled={disabled}
                            checked={text}
                            onChange={(e) => this.changeInputField(e, 'checkbox', 'keyInclude', record.uuid)}
                        />
                    );
                    return record.type === 'variable' ? dom : '--';
                }
            },
            {
                title: 'HTTP',
                dataIndex: 'sendSpace',
                key: 'sendSpace',
                width: 100,
                render: (text, record, index) => {
                    let dom = (
                        <TooltipSelect
                            isVirtual
                            onBlur={() => {
                                if (text === null || text === undefined) {
                                    this.changeFieldShowErrMsg('sendSpace', 2, index);
                                } else {
                                    this.changeFieldShowErrMsg('sendSpace', 1, index);
                                }
                            }}
                            className={isShowErrMsg['sendSpace' + index] === 2 ? 'highlightInput' : ''}
                            showSearch
                            style={{ width: '100%' }}
                            disabled={disabled || (isSync && current === 1 && this.props.isSql)}
                            optionFilterProp="children"
                            value={text ? text : undefined}
                            onChange={(e) => this.changeInputField(e, 'select', 'sendSpace', record.uuid, index)}>
                            <Option value="url">url</Option>
                            <Option value="header">header</Option>
                            <Option value="body">body</Option>
                            {isSync && this.props.isSql && [<Option value="sql">sql</Option>, <Option value="sqlParam">sqlParam</Option>]}
                        </TooltipSelect>
                    );
                    if (this.props.methodType === 'socket') {
                        dom = 'body';
                        record.sendSpace = 'body';
                    }
                    return dom;
                }
            },
            {
                title: I18N.addmodify.serviceinput.caoZuo, // 操作
                dataIndex: 'operate',
                key: 'operate',
                width: 60,
                align: 'center',
                fixed: 'right',
                render: (text, record) => {
                    let dom = (
                        <span className="u-operate" onClick={() => this.deleteInput(record.uuid)}>
                            <Icon type="delete" />
                        </span>
                    );
                    if (
                        modalType === 3 ||
                        record?.fromContract ||
                        (isSync && current === 1 && record.sendSpace === 'sql' && this.props.isSql) // 同步模式下，第一步的输入参数不允许删除
                    ) {
                        dom = '--';
                    }
                    return dom;
                }
            }
        ];

        return columns;
    };

    render() {
        const { modalType, disabled } = this.props;
        const { inputPage, inputConfig, serviceInputIsNull } = this.state;
        const columns = this.getColumns(modalType, disabled);



        // 计算当前页显示的数据
        const startIndex = (inputPage.curPage - 1) * inputPage.pageSize;
        const endIndex = startIndex + inputPage.pageSize;
        const currentPageData = inputConfig.slice(startIndex, endIndex);

        return (
            <div>
                <Table
                    size="small"
                    className={
                        serviceInputIsNull
                            ? modalType === 3
                                ? 'm-service-table2'
                                : 'm-service-table'
                            : modalType === 3
                            ? 'm-service-table2 inputHighLight'
                            : 'm-service-table inputHighLight'
                    }
                    columns={columns}
                    scroll={{
                        x: otp.tableScroll
                    }}
                    bordered
                    dataSource={currentPageData}
                    rowKey="uuid"
                    pagination={false}
                />
                {inputConfig.length > 0 && (
                    <div style={{ marginTop: 16, textAlign: 'right' }}>
                        <Pagination
                            current={inputPage.curPage}
                            pageSize={inputPage.pageSize}
                            total={inputConfig.length}
                            onChange={this.inputOnChange}
                            onShowSizeChange={this.inputOnChange}
                        />
                    </div>
                )}
                {modalType !== 3 && (
                    <div className="u-add" onClick={this.addInputData}>
                        <Icon type="plus-circle" />
                        {/* 新增字段 */}
                        {I18N.addmodify.serviceinput.xinZengZiDuan}
                    </div>
                )}
            </div>
        );
    }
}

export default connect((state) => ({
    globalStore: state.global,
    dataServiceListStore: state.dataServiceList
}))(ServiceInput);
