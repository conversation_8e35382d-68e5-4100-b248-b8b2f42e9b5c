import I18N from '@/utils/I18N';
import { PureComponent, Fragment, memo } from 'react';
import { connect } from 'dva';
import { cloneDeep, isEqual } from 'lodash';
import { Input, Select, Table, Icon, Tooltip, Checkbox } from 'tntd';
import TooltipSelect from '@tntd/tooltip-select';

import otp from './otp';

const Option = Select.Option;

// TooltipSelect的高阶组件
let MemoSelect = memo(
    (props) => {
        return <TooltipSelect {...props}>{props.children}</TooltipSelect>;
    },
    (prevProps, nextProps) => {
        if (
            prevProps.value === nextProps.value &&
            prevProps.disabled === nextProps.disabled &&
            isEqual(prevProps.options, nextProps.options)
        ) {
            return true;
        }
        return false;
    }
);

class ServiceOutput extends PureComponent {
    state = {
        outputPage: {
            curPage: 1,
            pageSize: 10
        },
        outputConfig: [],
        serviceInputIsNull: false,
        isShowErrMsg: {}
    };

    // 触发Form的onChange和校验
    triggerChange = (changedValue) => {
        const { onChange } = this.props;
        if (onChange) {
            onChange(changedValue);
        }
    };

    componentDidMount() {
        const { value = [] } = this.props;

        this.setState({
            outputConfig: value
        });
    }

    componentDidUpdate(prevProps) {
        if (prevProps.value !== this.props.value) {
            this.setState({
                outputConfig: this.props.value || []
            });
        }
    }

    // 服务出参分页
    outputOnChange = (curPage, pageSize) => {
        this.setState({
            outputPage: {
                curPage,
                pageSize
            }
        });
    };

    // 添加服务出参
    addOutputData = () => {
        const { outputPage, outputConfig } = this.state;
        const { current, onChange } = this.props;
        let paramsItem = {
            displayName: null, // 字段名称
            serviceParam: null, // 字段标识
            type: 'variable', // 字段类型(constant常量/variable变量)
            includeCheck: {
                checkType: 1, // 校验类型(1不为空命中/2具体值命中)
                checkValue: null // checkType=2时的值
            },
            checked: false
        };

        if (current === 1) {
            let copyOutputConfig = cloneDeep(outputConfig);
            copyOutputConfig.push(paramsItem);
            copyOutputConfig.forEach((item, index) => {
                item.uuid = index;
            });
            const curPage = Math.ceil(copyOutputConfig.length / outputPage.pageSize);
            this.setState({
                outputPage: {
                    curPage,
                    pageSize: outputPage.pageSize
                },
                outputConfig: copyOutputConfig
            });

            this.triggerChange(copyOutputConfig);
        } else {
            let copyAsyncOutputConfig = cloneDeep(outputConfig);
            copyAsyncOutputConfig.push(paramsItem);
            copyAsyncOutputConfig.forEach((item, index) => {
                item.uuid = index;
            });
            const curPage = Math.ceil(copyAsyncOutputConfig.length / outputPage.pageSize);
            this.setState({
                outputPage: {
                    curPage,
                    pageSize: outputPage.pageSize
                },
                outputConfig: copyAsyncOutputConfig
            });

            this.triggerChange(copyAsyncOutputConfig);
        }
    };

    // 删除服务出参
    deleteOutput(uuid) {
        const { current, onChange } = this.props;

        const { outputConfig } = this.state;

        if (current === 1) {
            let copyOutputConfig = cloneDeep(outputConfig);
            copyOutputConfig.splice(uuid, 1);
            copyOutputConfig.forEach((item, index) => {
                item.uuid = index;
            });

            this.setState({
                serviceOutputIsNull: true,
                outputConfig: copyOutputConfig
            });

            this.triggerChange(copyOutputConfig);
        } else {
            let copyAsyncOutputConfig = cloneDeep(outputConfig);
            copyAsyncOutputConfig.splice(uuid, 1);
            copyAsyncOutputConfig.forEach((item, index) => {
                item.uuid = index;
            });

            this.setState({
                serviceOutputIsNull: true,
                outputConfig: copyAsyncOutputConfig
            });
            this.triggerChange(copyAsyncOutputConfig);
        }
    }
    // 校验输入框函数
    changeFieldShowErrMsg = (field, flag, index) => {
        const { isShowErrMsg } = this.state;

        isShowErrMsg[field + index] = flag;

        this.setState({
            isShowErrMsg
        });
    };
    // 改变服务出参
    changeOutputField(e, type, field, uuid, index, children) {
        const { current, onChange } = this.props;

        const { outputConfig, isShowErrMsg } = this.state;

        let val = null;
        if (type === 'select') {
            val = e;
            if (val === null || val === undefined || val === '') {
                this.changeFieldShowErrMsg(field, 2, index);
            } else {
                isShowErrMsg[field + index] = 1;
                this.changeFieldShowErrMsg(field, 1, index);
            }
        }
        if (type === 'input') {
            val = e.target.value;
            if (val !== '') {
                this.changeFieldShowErrMsg(field, 1, index);
            } else {
                this.changeFieldShowErrMsg(field, 2, index);
            }
        }
        if (current === 1) {
            let copyOutputConfig = cloneDeep(outputConfig);
            if (field === 'type') {
                copyOutputConfig[uuid]['value'] = null;
                copyOutputConfig[uuid]['serviceParam'] = null;
            }
            if (type === 'checkbox') {
                val = e.target.checked;
                if (!val) {
                    // 重置
                    copyOutputConfig[uuid]['includeCheck']['checkType'] = 1;
                    copyOutputConfig[uuid]['includeCheck']['checkValue'] = null;
                }
            }
            if (field === 'checkType') {
                if (e === 1) {
                    copyOutputConfig[uuid]['includeCheck']['checkValue'] = null;
                }
                copyOutputConfig[uuid]['includeCheck'][field] = val;
            } else if (field === 'checkValue') {
                copyOutputConfig[uuid]['includeCheck'][field] = val;
            } else if (field === 'field') {
                copyOutputConfig[uuid]['dataType'] = children?.props?.item?.dataType;
                copyOutputConfig[uuid][field] = val;
            } else {
                copyOutputConfig[uuid][field] = val;
            }

            this.setState({
                outputConfig: copyOutputConfig
            });

            this.triggerChange(copyOutputConfig);
        } else {
            let copyAsyncOutputConfig = cloneDeep(outputConfig);
            if (field === 'type') {
                copyAsyncOutputConfig[uuid]['value'] = null;
                copyAsyncOutputConfig[uuid]['serviceParam'] = null;
            }
            if (type === 'checkbox') {
                val = e.target.checked;
                if (!val) {
                    // 重置
                    copyAsyncOutputConfig[uuid]['includeCheck']['checkType'] = 1;
                    copyAsyncOutputConfig[uuid]['includeCheck']['checkValue'] = null;
                }
            }
            if (field === 'checkType') {
                if (e === 1) {
                    copyAsyncOutputConfig[uuid]['includeCheck']['checkValue'] = null;
                }
                copyAsyncOutputConfig[uuid]['includeCheck'][field] = val;
            } else if (field === 'checkValue') {
                copyAsyncOutputConfig[uuid]['includeCheck'][field] = val;
            } else {
                copyAsyncOutputConfig[uuid][field] = val;
            }
            this.setState({
                outputConfig: copyAsyncOutputConfig
            });
            this.triggerChange(copyAsyncOutputConfig);
        }

        this.setState({
            serviceOutputIsNull: true
        });
    }
    getColumns = (modalType, disabled) => {
        const { systemList, isSync, current, isIntegrationTG } = this.props;

        const { outputConfig, isShowErrMsg } = this.state;

        let list = cloneDeep(outputConfig);
        const fields = list.map((item) => item.field);

        let columns = [
            {
                title: (
                    <Fragment>
                        {/* 系统字段 */}
                        {I18N.addmodify.serviceoutput.xiTongZiDuan}
                        <Tooltip title={I18N.addmodify.serviceoutput.hePingTaiTiGong}>
                            <Icon type="question-circle" />
                        </Tooltip>
                    </Fragment>
                ),
                dataIndex: 'field',
                width: 160,
                render: (text, record, index) => {
                    return (
                        <MemoSelect
                            options={systemList}
                            isVirtual
                            onBlur={() => {
                                if (record.field === null || record.field === undefined || record.field === '') {
                                    this.changeFieldShowErrMsg('field', 2, index + 'outPut');
                                } else {
                                    this.changeFieldShowErrMsg('field', 1, index + 'outPut');
                                }
                            }}
                            className={isShowErrMsg['field' + index + 'outPut'] === 2 ? 'highlightInput' : ''}
                            showSearch
                            style={{ width: '160px' }}
                            disabled={disabled}
                            // dropdownMatchSelectWidth={false}
                            // dropdownStyle={{ width: 350 }}
                            optionFilterProp="children"
                            value={record.field ? record.field : undefined}
                            onChange={(e, children) =>
                                this.changeOutputField(e, 'select', 'field', record.uuid, index + 'outPut', children)
                            }>
                            {systemList &&
                                systemList.map((item, index) => {
                                    return (
                                        <Option value={item.name} key={index} disabled={fields.includes(item.name)} item={item}>
                                            {item.displayName}【{item.name}】
                                        </Option>
                                    );
                                })}
                        </MemoSelect>
                    );
                }
            },
            {
                title: (
                    <Fragment>
                        {/* 映射 */}
                        {I18N.addmodify.serviceoutput.yingShe}
                        <Tooltip title={I18N.addmodify.serviceoutput.baFuWuChuCan}>
                            <Icon type="question-circle" />
                        </Tooltip>
                    </Fragment>
                ),
                dataIndex: 'diraction',
                width: 100,
                align: 'center',
                render: () => {
                    return <Icon type="arrow-left" />;
                }
            },
            {
                title: I18N.addmodify.serviceoutput.canShuBiaoZhi, // 参数标识
                dataIndex: 'serviceParam',
                key: 'serviceParam',
                width: 100,
                render: (text, record, index) => {
                    let dom = (
                        <Tooltip title={text}>
                            <Input
                                onBlur={() => {
                                    if (text === null || text === undefined || text === '') {
                                        this.changeFieldShowErrMsg('serviceParam', 2, index + 'outPut');
                                    } else {
                                        this.changeFieldShowErrMsg('serviceParam', 1, index + 'outPut');
                                    }
                                }}
                                className={isShowErrMsg['serviceParam' + index + 'outPut'] === 2 ? 'highlightInput' : ''}
                                disabled={disabled}
                                value={text}
                                onChange={(e) => this.changeOutputField(e, 'input', 'serviceParam', record.uuid, index + 'outPut')}
                            />
                        </Tooltip>
                    );
                    if (record.type === 'constant') {
                        // 定值
                        dom = (
                            <Tooltip title={record.value}>
                                <Input
                                    onBlur={() => {
                                        if (record.value === null || record.value === undefined || record.value === '') {
                                            this.changeFieldShowErrMsg('value', 2, index + 'outPut');
                                        } else {
                                            this.changeFieldShowErrMsg('value', 1, index + 'outPut');
                                        }
                                    }}
                                    className={isShowErrMsg['value' + index + 'outPut'] === 2 ? 'highlightInput' : ''}
                                    disabled={disabled}
                                    value={record.value}
                                    placeholder={I18N.addmodify.serviceoutput.qingShuRuDingZhi} // 请输入定值
                                    onChange={(e) => this.changeOutputField(e, 'input', 'value', record.uuid, index + 'outPut')}
                                />
                            </Tooltip>
                        );
                    }
                    return dom;
                }
            },
            {
                title: I18N.addmodify.serviceoutput.canShuLeiXing, // 参数类型
                dataIndex: 'type',
                key: 'type',
                width: 80,
                render: (text, record, index) => {
                    let dom = (
                        <TooltipSelect
                            isVirtual
                            onBlur={() => {
                                if (text === null || text === undefined || text === '') {
                                    this.changeFieldShowErrMsg('text', 2, index + 'outPut');
                                } else {
                                    this.changeFieldShowErrMsg('text', 1, index + 'outPut');
                                }
                            }}
                            className={isShowErrMsg['text' + index + 'outPut'] === 2 ? 'highlightInput' : ''}
                            showSearch
                            style={{ width: '100%' }}
                            disabled={disabled}
                            optionFilterProp="children"
                            value={text ? text : undefined}
                            onChange={(e) => this.changeOutputField(e, 'select', 'type', record.uuid, index + 'outPut')}>
                            <Option value={'variable'}>
                                {I18N.addmodify.serviceoutput.bianLiang}
                                {/* 变量 */}
                            </Option>
                            <Option value={'constant'}>
                                {I18N.addmodify.serviceoutput.dingZhi}
                                {/* 定值 */}
                            </Option>
                        </TooltipSelect>
                    );
                    return dom;
                }
            },
            {
                title: (
                    <>
                        {I18N.addmodify.serviceoutput.chaDeZiDuan}
                        <Tooltip title={I18N.addmodify.serviceoutput.ruGuoJieKouWei}>
                            <Icon className="ml10 mr10" type="question-circle" />
                        </Tooltip>
                    </>
                ), // 查得字段
                dataIndex: 'includeCheck',
                key: 'includeCheck',
                width: 190,
                render: (text, record) => {
                    let dom = (
                        <span>
                            <Checkbox
                                disabled={disabled}
                                checked={record.checked}
                                onChange={(e) => this.changeOutputField(e, 'checkbox', 'checked', record.uuid)}
                            />
                            {record.checked && (
                                <TooltipSelect
                                    isVirtual
                                    showSearch
                                    style={{ width: '80px', margin: '0 10px' }}
                                    disabled={disabled}
                                    optionFilterProp="children"
                                    dropdownMatchSelectWidth={false}
                                    value={record.includeCheck.checkType ? record.includeCheck.checkType : undefined}
                                    onChange={(e) => this.changeOutputField(e, 'select', 'checkType', record.uuid)}>
                                    <Option value={1}>
                                        {I18N.addmodify.serviceoutput.feiKong}
                                        {/* 非空 */}
                                    </Option>
                                    <Option value={2}>
                                        {I18N.addmodify.serviceoutput.quZhi}
                                        {/* 取值 */}
                                    </Option>
                                </TooltipSelect>
                            )}
                            {record?.includeCheck?.checkType === 2 && (
                                <Tooltip title={record.includeCheck.checkValue}>
                                    <Input
                                        disabled={disabled}
                                        style={{ width: '60px' }}
                                        value={record.includeCheck.checkValue}
                                        onChange={(e) => this.changeOutputField(e, 'input', 'checkValue', record.uuid)}
                                    />
                                </Tooltip>
                            )}
                        </span>
                    );
                    return dom;
                }
            },
            {
                title: () => {
                    return (
                        <>
                            <span>{I18N.addmodify.serviceoutput.morenzhi}</span>
                            <Tooltip title={I18N.addmodify.serviceoutput.morenzhiTitle}>
                                <Icon className="ml10 mr10" type="question-circle" />
                            </Tooltip>
                        </>
                    );
                },
                dataIndex: 'defaultValue',
                width: 140,
                render: (text, record, index) => {
                    return (
                        <Input
                            disabled={disabled}
                            value={text}
                            onChange={(e) => this.changeOutputField(e, 'input', 'defaultValue', record.uuid)}
                        />
                    );
                }
            },
            {
                title: () => {
                    return (
                        <>
                            <span>{I18N.addmodify.serviceoutput.geShiJiaoYan}</span>
                            <Tooltip
                                title={
                                    <>
                                        <div>{I18N.addmodify.serviceoutput.shuRuZhengZeDui}</div>
                                        <div>{I18N.addmodify.serviceoutput.shiLi}</div>
                                        <div>
                                            {I18N.addmodify.serviceoutput.shenFenZhengHaoD}
                                            {5}(18|19|20|(3\d))\d{2}((0[1-9])|(1[0-2]))(([0-2][1-9])|10|20|30|31)\d
                                            {3}[0-9Xx]$
                                        </div>
                                        <div>
                                            {' '}
                                            {I18N.addmodify.serviceoutput.shouJiHaoD}
                                            {9}${' '}
                                        </div>
                                        <div>{I18N.addmodify.serviceoutput.youXiangHaoAZ}</div>
                                    </>
                                }>
                                <Icon className="ml10 mr10" type="question-circle" />
                            </Tooltip>
                        </>
                    );
                },
                dataIndex: 'formatCheck',
                width: 140,
                render: (text, record, index) => {
                    //只对字符类型的字段进行正则校验，其他显示 - -
                    return record?.dataType === 1 ? (
                        <>
                            <Input
                                disabled={disabled}
                                value={text}
                                onChange={(e) => this.changeOutputField(e, 'input', 'formatCheck', record.uuid, index + 'outPut')}
                            />
                        </>
                    ) : (
                        '- -'
                    );
                }
            },
            {
                title: I18N.addmodify.serviceoutput.caoZuo, // 操作
                dataIndex: 'operate',
                key: 'operate',
                width: 60,
                align: 'center',
                fixed: 'right',
                render: (text, record) => {
                    let dom = (
                        <span className="u-operate" onClick={() => this.deleteOutput(record.uuid)}>
                            <Icon type="delete" />
                        </span>
                    );
                    if (modalType === 3) dom = '--';
                    return dom;
                }
            }
        ];

        if (isIntegrationTG) {
            columns = columns.filter((v) => v.dataIndex !== 'includeCheck');
        }
        return columns;
    };

    render() {
        const { outputPage, outputConfig, serviceOutputIsNull } = this.state;
        const { modalType, disabled } = this.props;
        const columns = this.getColumns(modalType, disabled);

        return (
            <div>
                <Table
                    size="small"
                    className={
                        serviceOutputIsNull
                            ? modalType === 3
                                ? 'm-service-table2'
                                : 'm-service-table'
                            : modalType === 3
                            ? 'm-service-table2 inputHighLight'
                            : 'm-service-table inputHighLight'
                    }
                    columns={columns}
                    dataSource={outputConfig}
                    rowKey="uuid"
                    scroll={{
                        x: otp.tableScroll
                    }}
                    bordered
                    pagination={{
                        current: outputPage.curPage,
                        pageSize: outputPage.pageSize,
                        total: outputConfig.length,
                        onChange: this.outputOnChange,
                        showSizeChanger: true,
                        pageSizeOptions: ['5', '10', '20', '30'],
                        onShowSizeChange: this.outputOnChange
                    }}
                />
                {modalType !== 3 && (
                    <div className="u-add" onClick={this.addOutputData}>
                        <Icon type="plus-circle" />
                        {/* 新增字段 */}
                        {I18N.addmodify.serviceoutput.xinZengZiDuan}
                    </div>
                )}
            </div>
        );
    }
    componentWillUnmount() {
        const { isShowErrMsg } = this.state;

        let obj = cloneDeep(isShowErrMsg);

        for (let i in obj) {
            obj[i] = 1;
        } //页面销毁重置所有高亮状态

        this.setState({
            isShowErrMsg: obj
        });
    }
}

export default connect((state) => ({
    globalStore: state.global,
    dataServiceListStore: state.dataServiceList
}))(ServiceOutput);
